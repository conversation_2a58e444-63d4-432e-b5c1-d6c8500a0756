
import akshare as ak
import streamlit as st
from langchain.prompts import PromptTemplate
from langchain.chains import <PERSON><PERSON>hain
from langchain_openai import ChatOpenAI
import re
import jieba
# 智谱API配置
zhipu_api_key = "你的key"
llm = ChatOpenAI(
    temperature=0.95,
    model="glm-4-flash",
    openai_api_key=zhipu_api_key,
    openai_api_base="https://open.bigmodel.cn/api/paas/v4/",
    max_tokens=2048
)
# 文本预处理配置
STOP_WORDS = {
    '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '而', '及', '可以',
    '这', '一个', '为', '之', '与', '等', '也', '会', '要', '于', '中', '对', '并', '其',
    '或', '后', '但', '被', '让', '说', '去', '又', '已', '向', '使', '该', '将', '到',
    '应', '与', '了', '这', '我们', '他们', '自己', '这个', '这些', '这样', '因为', '所以'
}
# LangChain提示模板
prompt_template = """
作为专业金融分析师，请基于以下资讯摘要给出投资建议：
资讯标题：{news_title}
资讯摘要：{news_summary}
请按以下结构化输出：
### 核心逻辑
1. 市场影响：用不超过3句话说明资讯的核心影响
2. 关键驱动因素：列出3-5个关键驱动因素
### 板块机会
按受益程度列出前三大关联板块，每个板块包含：
- 板块名称
- 受益逻辑
- 受益强度评估（高/中/低）
### 投资建议
以表格形式推荐5只关联度最高的个股：
| 股票代码 | 股票名称 | 关联逻辑 | 操作建议 |
|---------|---------|--------|--------|
"""
prompt = PromptTemplate(
    input_variables=["news_title", "news_summary"],
    template=prompt_template
)
chain = LLMChain(llm=llm, prompt=prompt)
# 文本处理函数
def preprocess_text(text):
    """资讯文本预处理"""
    text = re.sub(r'[^\w\s]', '', text)
    words = [word for word in jieba.cut(text)
             if word not in STOP_WORDS and len(word) > 1]
    return " ".join(words)
# Streamlit界面
def app():
    # 主界面
    st.title("资讯分析系统")
    try:
        # 获取实时资讯
        news_df = ak.stock_info_global_em()[['标题', '摘要', '发布时间']].dropna().head(20)
        # 资讯选择器
        selected_news = st.selectbox("选择分析资讯", news_df['标题'])
        news_detail = news_df[news_df['标题'] == selected_news].iloc[0]
        # 文本预处理
        with st.expander("原始文本预处理", expanded=False):
            cleaned_text = preprocess_text(news_detail['摘要'])
            st.code(cleaned_text[:500] + "...", language='text')
        # 生成分析报告
        if st.button("生成AI投研报告", type="primary"):
            with st.spinner('AI分析中...'):
                analysis = chain.run({
                    "news_title": selected_news,
                    "news_summary": cleaned_text[:1000]  # 摘要截取优化
                })
                # 结构化展示
                st.subheader("📊 AI投研报告")
                st.markdown("---")
                try:
                    # 核心逻辑解析
                    core_logic = analysis.split("### 核心逻辑")[1].split("### 板块机会")[0]
                    st.markdown(f"## 核心逻辑\n{core_logic}")
                    # 板块机会展示
                    sectors = analysis.split("### 板块机会")[1].split("### 投资建议")[0]
                    st.markdown(f"## 板块机会\n{sectors}")
                    # 投资建议表格
                    st.markdown("## 投资建议")
                    st.markdown(analysis.split("### 投资建议")[1])
                except Exception as e:
                    st.warning("结构解析异常，展示原始输出：")
                    st.code(analysis, language='markdown')
    except Exception as e:
        st.error(f"系统异常：{str(e)}")
if __name__ == "__main__":
    app()