# -*- coding: utf-8 -*-
import streamlit as st
import akshare as ak
import pandas as pd
import numpy as np
import traceback
import sys
import os
import re
from datetime import datetime, timedelta
import plotly.graph_objs as go
import plotly.express as px
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.model_selection import train_test_split, cross_val_score, TimeSeriesSplit
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
import seaborn as sns


# 设置动态默认日期
today = datetime.now()
default_start = today - timedelta(days=365)
default_end = today

# 历史记录文件路径
HISTORY_FILE = "stock_query_history.txt"

# 读取历史查询记录
def read_history():
    if not os.path.exists(HISTORY_FILE):
        return []

    try:
        with open(HISTORY_FILE, "r", encoding="utf-8") as f:
            lines = f.readlines()

        history = []
        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 解析记录行，包括时间戳
            match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) - 股票代码: (\d+), 股票名称: (.+)$', line)
            if match:
                timestamp = match.group(1)
                code = match.group(2)
                name = match.group(3)
                history.append({"timestamp": timestamp, "code": code, "name": name})

        # 按时间戳排序（最新的在最后）
        history.sort(key=lambda x: x["timestamp"])

        # 去重，保留最新的记录
        unique_history = []
        seen_codes = set()
        for item in reversed(history):
            if item["code"] not in seen_codes:
                seen_codes.add(item["code"])
                unique_history.append({"code": item["code"], "name": item["name"]})

        return list(reversed(unique_history))  # 恢复原来的顺序（最新的在最前）
    except Exception as e:
        st.error(f"读取历史记录出错: {str(e)}")
        return []

# 保存查询记录
def save_history(code, name):
    try:
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        record = f"{timestamp} - 股票代码: {code}, 股票名称: {name}\n"

        with open(HISTORY_FILE, "a", encoding="utf-8") as f:
            f.write(record)
    except Exception as e:
        st.error(f"保存历史记录出错: {str(e)}")

# 获取股票名称
def get_stock_name(code):
    try:
        # 使用akshare获取股票名称
        stock_info = ak.stock_individual_info_em(symbol=code)
        if not stock_info.empty:
            return stock_info.iloc[0, 0]  # 第一行第一列是股票名称
        return "未知"
    except:
        return "未知"

# 获取股票数据
def load_data(code, start, end):
    df = ak.stock_zh_a_hist(
        symbol=code,
        period="daily",
        start_date=start.strftime("%Y%m%d"),
        end_date=end.strftime("%Y%m%d"),
        #adjust="hfq"
        adjust="qfq"
    )
    # 转换日期并排序
    df['日期'] = pd.to_datetime(df['日期'])
    df = df.sort_values('日期')
    df.set_index('日期', inplace=True)
    return df


# 数据预处理（优化NaN处理）
def preprocess_data(df):
    # 检查输入数据是否为空
    if df.empty:
        st.error("输入数据为空，无法进行预处理")
        return df

    # 打印原始数据形状，用于调试
    st.write(f"原始数据形状: {df.shape}")

    # 基本技术指标计算
    # 移动平均线 - 减少min_periods以避免生成太多NaN
    df['5日均线'] = df['收盘'].rolling(5, min_periods=1).mean()
    df['10日均线'] = df['收盘'].rolling(10, min_periods=1).mean()
    df['20日均线'] = df['收盘'].rolling(20, min_periods=1).mean()
    df['60日均线'] = df['收盘'].rolling(60, min_periods=1).mean()

    # 价格变化率
    df['1日涨跌幅'] = df['收盘'].pct_change(1).fillna(0)  # 填充NaN
    df['5日涨跌幅'] = df['收盘'].pct_change(5).fillna(0)  # 填充NaN
    df['10日涨跌幅'] = df['收盘'].pct_change(10).fillna(0)  # 填充NaN

    # 成交量指标
    df['成交量5日均线'] = df['成交量'].rolling(5, min_periods=1).mean()
    df['成交量比率'] = df['成交量'] / df['成交量5日均线'].replace(0, 1e-6)  # 避免除零

    # 波动率指标
    df['5日波动率'] = df['收盘'].rolling(5, min_periods=1).std() / df['收盘'].rolling(5, min_periods=1).mean().replace(0, 1e-6)
    df['10日波动率'] = df['收盘'].rolling(10, min_periods=1).std() / df['收盘'].rolling(10, min_periods=1).mean().replace(0, 1e-6)

    # MACD指标
    exp1 = df['收盘'].ewm(span=12, adjust=False).mean()
    exp2 = df['收盘'].ewm(span=26, adjust=False).mean()
    df['MACD'] = exp1 - exp2
    df['MACD信号线'] = df['MACD'].ewm(span=9, adjust=False).mean()
    df['MACD柱状'] = df['MACD'] - df['MACD信号线']

    # RSI计算（防止除零错误）
    delta = df['收盘'].diff(1).fillna(0)
    gain = delta.where(delta > 0, 0.0)  # 避免负值
    loss = -delta.where(delta < 0, 0.0)

    # 计算不同周期的RSI
    for period in [6, 14, 21]:
        avg_gain = gain.rolling(period, min_periods=1).mean()
        avg_loss = loss.rolling(period, min_periods=1).mean().replace(0, 1e-6)  # 处理全零情况
        rs = avg_gain / avg_loss
        df[f'RSI_{period}'] = 100 - (100 / (1 + rs))

    # 布林带
    df['布林中轨'] = df['收盘'].rolling(20, min_periods=1).mean()
    df['布林标准差'] = df['收盘'].rolling(20, min_periods=1).std()
    df['布林上轨'] = df['布林中轨'] + 2 * df['布林标准差']
    df['布林下轨'] = df['布林中轨'] - 2 * df['布林标准差']
    df['布林带宽'] = (df['布林上轨'] - df['布林下轨']) / df['布林中轨'].replace(0, 1e-6)

    # 安全计算布林位置
    denom_boll = df['布林上轨'] - df['布林下轨']
    denom_boll = denom_boll.replace(0, 1e-6)  # 避免除零
    df['布林位置'] = (df['收盘'] - df['布林下轨']) / denom_boll

    # KDJ指标
    low_min = df['最低'].rolling(9, min_periods=1).min()
    high_max = df['最高'].rolling(9, min_periods=1).max()
    df['KDJ_K'] = 50
    df['KDJ_D'] = 50

    # 避免除零错误
    denom = high_max - low_min
    denom = denom.replace(0, 1e-6)

    df['KDJ_RSV'] = (df['收盘'] - low_min) / denom * 100

    # 计算KDJ - 使用更安全的方法
    for i in range(1, len(df)):
        try:
            df.iloc[i, df.columns.get_loc('KDJ_K')] = 2/3 * df.iloc[i-1, df.columns.get_loc('KDJ_K')] + 1/3 * df.iloc[i, df.columns.get_loc('KDJ_RSV')]
            df.iloc[i, df.columns.get_loc('KDJ_D')] = 2/3 * df.iloc[i-1, df.columns.get_loc('KDJ_D')] + 1/3 * df.iloc[i, df.columns.get_loc('KDJ_K')]
        except:
            # 如果出现错误，保持默认值
            pass

    df['KDJ_J'] = 3 * df['KDJ_K'] - 2 * df['KDJ_D']

    # 趋势指标
    df['收盘_5日趋势'] = (df['收盘'] > df['5日均线']).astype(int)
    df['收盘_10日趋势'] = (df['收盘'] > df['10日均线']).astype(int)
    df['收盘_20日趋势'] = (df['收盘'] > df['20日均线']).astype(int)
    df['5日_10日趋势'] = (df['5日均线'] > df['10日均线']).astype(int)
    df['10日_20日趋势'] = (df['10日均线'] > df['20日均线']).astype(int)

    # 生成标签
    df['涨跌标签'] = np.where(df['收盘'].shift(-1) > df['收盘'], 1, 0)

    # 填充NaN值
    df = df.fillna(method='ffill').fillna(method='bfill').fillna(0)

    # 检查处理后的数据是否为空
    if df.empty:
        st.error("预处理后数据为空，请尝试增加数据量或减少技术指标")
        return df

    # 打印处理后的数据形状，用于调试
    st.write(f"预处理后数据形状: {df.shape}")

    return df.copy()


def app():
    # 创建侧边栏显示历史记录
    st.sidebar.title("历史查询记录")
    history = read_history()

    # 在侧边栏显示历史记录列表
    if history:
        st.sidebar.subheader("最近查询的股票")
        # 显示最近10条记录（从最新到最旧）
        for item in reversed(history[:10]):
            st.sidebar.write(f"📊 {item['code']} - {item['name']}")

        # 显示历史记录总数
        total_records = len(history)
        if total_records > 10:
            st.sidebar.caption(f"显示最近10条记录（共{total_records}条）")
    else:
        st.sidebar.write("暂无历史记录")

    # 主界面
    st.title("股票分析应用")

    # 创建两列布局
    col1, col2 = st.columns([3, 1])

    # 在第一列显示股票代码输入框
    with col1:
        stock_code = st.text_input("输入股票代码（例如：600519）", "600519")

    # 在第二列显示历史记录下拉菜单
    with col2:
        if history:
            # 创建历史记录选项列表
            history_options = ["选择历史记录..."] + [f"{item['code']} - {item['name']}" for item in history]
            selected_history = st.selectbox("历史记录", history_options, index=0)

            # 如果用户选择了历史记录
            if selected_history != "选择历史记录...":
                # 从选择的历史记录中提取股票代码
                selected_code = selected_history.split(" - ")[0]
                # 更新股票代码输入框
                stock_code = selected_code

    start_date = st.date_input("开始日期", default_start)
    end_date = st.date_input("结束日期", default_end)

    try:
        # 时间有效性验证
        if start_date >= end_date:
            st.error("错误：开始日期必须早于结束日期！")
        else:
            df = load_data(stock_code, start_date, end_date)

            # 获取股票名称并保存到历史记录
            stock_name = get_stock_name(stock_code)
            save_history(stock_code, stock_name)

            if df.empty:
                st.error("数据获取失败，请检查：1.股票代码有效性 2.日期范围包含交易日")
            else:
                # 显示最新数据
                st.subheader(f"{stock_code} 最新10个交易日数据")
                st.dataframe(df.tail(10).style.format(precision=2),
                             height=400, use_container_width=True)

                # 数据预处理
                processed_df = preprocess_data(df)

                # 特征工程
                try:
                    # 检查处理后的数据是否足够
                    if len(processed_df) < 60:  # 至少需要60天的数据才能计算所有指标
                        st.warning(f"数据量不足，当前仅有 {len(processed_df)} 条记录，建议至少需要60条以上")
                        # 如果数据不足，使用更少的特征
                        features = ['收盘', '成交量', '5日均线', '20日均线', 'RSI_14']
                    else:
                        # 选择更多有用的特征
                        features = [
                            '收盘', '成交量', '5日均线', '10日均线', '20日均线', '60日均线',
                            '1日涨跌幅', '5日涨跌幅', '10日涨跌幅',
                            '成交量比率', '5日波动率', '10日波动率',
                            'MACD', 'MACD信号线', 'MACD柱状',
                            'RSI_6', 'RSI_14', 'RSI_21',
                            '布林带宽', '布林位置',
                            'KDJ_K', 'KDJ_D', 'KDJ_J',
                            '收盘_5日趋势', '收盘_10日趋势', '收盘_20日趋势',
                            '5日_10日趋势', '10日_20日趋势'
                        ]

                    # 确保所有特征都在数据框中
                    available_features = [f for f in features if f in processed_df.columns]
                    if len(available_features) < len(features):
                        missing_features = set(features) - set(available_features)
                        st.warning(f"以下特征不可用: {', '.join(missing_features)}")
                        features = available_features

                    # 检查特征列表是否为空
                    if not features:
                        st.error("没有可用的特征进行模型训练")
                        return

                    # 提取特征和目标变量
                    X = processed_df[features]
                    y = processed_df['涨跌标签']

                    # 检查数据是否为空
                    if X.empty or len(X) == 0:
                        st.error("特征数据为空，无法进行模型训练")
                        return

                    # 检查是否有无穷值或NaN
                    if X.isnull().any().any() or np.isinf(X.values).any():
                        st.warning("数据中存在NaN或无穷值，将进行填充")
                        X = X.fillna(0).replace([np.inf, -np.inf], 0)

                    # 特征标准化
                    st.write(f"特征数据形状: {X.shape}")
                    scaler = StandardScaler()
                    X_scaled = scaler.fit_transform(X)
                    X_scaled_df = pd.DataFrame(X_scaled, index=X.index, columns=X.columns)

                    # 时间序列分割
                    # 确保数据量足够进行交叉验证
                    if len(X) < 10:
                        st.error("数据量太少，无法进行交叉验证")
                        return

                    n_splits = min(5, len(X) // 10)  # 确保每个分割至少有10个样本
                    tscv = TimeSeriesSplit(n_splits=max(2, n_splits))

                    # 创建模型选项
                    model_options = {
                        "随机森林": RandomForestClassifier(n_estimators=100, max_depth=5, random_state=42),
                        "梯度提升树": GradientBoostingClassifier(n_estimators=100, learning_rate=0.1, max_depth=3, random_state=42)
                    }

                    # 模型选择
                    model_choice = st.selectbox("选择模型", list(model_options.keys()))
                    model = model_options[model_choice]

                    # 确保数据量足够进行训练测试集分割
                    if len(X) < 5:
                        st.error("数据量太少，无法进行模型训练")
                        return

                    # 模型训练
                    test_size = min(0.2, 1 - 3/len(X))  # 确保训练集至少有3个样本
                    X_train, X_test, y_train, y_test = train_test_split(
                        X_scaled_df, y, test_size=test_size, shuffle=False
                    )

                    # 检查训练集和测试集是否为空
                    if len(X_train) == 0 or len(X_test) == 0:
                        st.error("训练集或测试集为空，无法进行模型训练")
                        return

                    # 使用交叉验证评估模型
                    with st.spinner('正在进行交叉验证...'):
                        try:
                            cv_scores = cross_val_score(model, X_scaled_df, y, cv=tscv, scoring='accuracy')
                            st.write(f"交叉验证准确率: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")
                        except Exception as e:
                            st.error(f"交叉验证出错: {str(e)}")
                            st.code(traceback.format_exc())

                    # 训练最终模型
                    with st.spinner('正在训练最终模型...'):
                        try:
                            model.fit(X_train, y_train)

                            # 在测试集上评估
                            y_pred = model.predict(X_test)
                            y_pred_proba = model.predict_proba(X_test)[:, 1]

                            # 计算各种评估指标
                            accuracy = accuracy_score(y_test, y_pred)
                            precision = precision_score(y_test, y_pred, zero_division=0)
                            recall = recall_score(y_test, y_pred, zero_division=0)
                            f1 = f1_score(y_test, y_pred, zero_division=0)

                            # 计算混淆矩阵
                            cm = confusion_matrix(y_test, y_pred)

                            st.success("模型训练成功！")
                        except Exception as e:
                            st.error(f"模型训练出错: {str(e)}")
                            st.code(traceback.format_exc())
                            return

                except Exception as e:
                    st.error(f"特征工程或模型训练过程中出错: {str(e)}")
                    st.code(traceback.format_exc())
                    return

                # 界面展示
                try:
                    st.subheader("模型分析")

                    # 显示评估指标
                    col1, col2, col3, col4 = st.columns(4)
                    col1.metric("测试集准确率", f"{accuracy * 100:.2f}%")
                    col2.metric("精确率", f"{precision * 100:.2f}%")
                    col3.metric("召回率", f"{recall * 100:.2f}%")
                    col4.metric("F1分数", f"{f1 * 100:.2f}%")

                    # 显示混淆矩阵
                    st.subheader("混淆矩阵")
                    cm_df = pd.DataFrame(cm,
                                        index=['实际: 下跌', '实际: 上涨'],
                                        columns=['预测: 下跌', '预测: 上涨'])
                    st.write(cm_df)

                    # 特征重要性
                    if hasattr(model, 'feature_importances_'):
                        st.subheader("特征重要性")
                        importance = pd.DataFrame({
                            '特征': X.columns,
                            '重要性': model.feature_importances_
                        }).sort_values('重要性', ascending=False)

                        # 显示前15个最重要的特征，但不超过实际特征数量
                        top_n = min(15, len(importance))
                        fig, ax = plt.subplots(figsize=(10, 6))
                        sns.barplot(x='重要性', y='特征', data=importance.head(top_n), ax=ax)
                        plt.title('特征重要性排名')
                        st.pyplot(fig)

                    # 预测明日走势
                    if len(X_scaled_df) > 0:
                        latest_data = X_scaled_df.iloc[-1:].copy()
                        prediction = model.predict(latest_data)[0]
                        prediction_proba = model.predict_proba(latest_data)[0][1]

                        # 显示预测结果
                        st.subheader("预测结果")
                        col1, col2 = st.columns(2)
                        col1.metric("最新收盘价", f"{processed_df['收盘'].iloc[-1]:.2f}")
                        col2.metric("预测明日",
                                "看涨" if prediction == 1 else "看跌",
                                delta=f"{prediction_proba * 100:.2f}%")
                    else:
                        st.warning("没有足够的数据进行预测")

                except Exception as e:
                    st.error(f"显示模型分析结果时出错: {str(e)}")
                    st.code(traceback.format_exc())

                # 交互式图表
                try:
                    st.subheader("技术指标可视化")

                    # 创建选项卡
                    tab1, tab2, tab3, tab4 = st.tabs(["K线图", "技术指标", "相关性分析", "预测结果分析"])

                    with tab1:
                        try:
                            # K线图与均线
                            st.subheader("K线图与均线")
                            fig = go.Figure()
                            fig.add_trace(go.Candlestick(
                                x=processed_df.index,
                                open=processed_df['开盘'],
                                high=processed_df['最高'],
                                low=processed_df['最低'],
                                close=processed_df['收盘'],
                                increasing_line_color='red',
                                decreasing_line_color='green',
                                name='K线'
                            ))

                            # 添加多条均线
                            ma_colors = {'5日均线': 'blue', '10日均线': 'orange', '20日均线': 'purple', '60日均线': 'gray'}
                            for ma, color in ma_colors.items():
                                if ma in processed_df.columns:
                                    fig.add_trace(go.Scatter(
                                        x=processed_df.index,
                                        y=processed_df[ma],
                                        line=dict(color=color, width=1),
                                        name=ma
                                    ))

                            # 添加布林带
                            if '布林上轨' in processed_df.columns and '布林下轨' in processed_df.columns:
                                fig.add_trace(go.Scatter(
                                    x=processed_df.index,
                                    y=processed_df['布林上轨'],
                                    line=dict(color='rgba(250, 128, 114, 0.7)', width=1, dash='dot'),
                                    name='布林上轨'
                                ))
                                fig.add_trace(go.Scatter(
                                    x=processed_df.index,
                                    y=processed_df['布林下轨'],
                                    line=dict(color='rgba(173, 216, 230, 0.7)', width=1, dash='dot'),
                                    name='布林下轨'
                                ))

                            fig.update_layout(
                                height=600,
                                xaxis_rangeslider_visible=False,
                                hovermode='x unified',
                                title="K线图与均线分析"
                            )
                            st.plotly_chart(fig, use_container_width=True)
                        except Exception as e:
                            st.error(f"绘制K线图时出错: {str(e)}")
                            st.code(traceback.format_exc())

                    with tab2:
                        try:
                            # 技术指标可视化
                            st.subheader("选择技术指标")

                            # 创建技术指标选择器
                            indicator_options = {
                                "RSI指标": ['RSI_6', 'RSI_14', 'RSI_21'],
                                "MACD指标": ['MACD', 'MACD信号线', 'MACD柱状'],
                                "KDJ指标": ['KDJ_K', 'KDJ_D', 'KDJ_J'],
                                "成交量分析": ['成交量', '成交量5日均线', '成交量比率'],
                                "波动率指标": ['5日波动率', '10日波动率']
                            }

                            # 过滤掉不存在的指标
                            filtered_options = {}
                            for key, indicators in indicator_options.items():
                                available_indicators = [ind for ind in indicators if ind in processed_df.columns]
                                if available_indicators:
                                    filtered_options[key] = available_indicators

                            if not filtered_options:
                                st.warning("没有可用的技术指标")
                            else:
                                selected_indicator = st.selectbox("选择要显示的技术指标", list(filtered_options.keys()))

                                # 绘制所选技术指标
                                fig = go.Figure()

                                for indicator in filtered_options[selected_indicator]:
                                    fig.add_trace(go.Scatter(
                                        x=processed_df.index,
                                        y=processed_df[indicator],
                                        name=indicator
                                    ))

                                fig.update_layout(
                                    height=400,
                                    hovermode='x unified',
                                    title=f"{selected_indicator}分析"
                                )
                                st.plotly_chart(fig, use_container_width=True)
                        except Exception as e:
                            st.error(f"绘制技术指标时出错: {str(e)}")
                            st.code(traceback.format_exc())

                    with tab3:
                        try:
                            # 相关性分析
                            st.subheader("特征相关性分析")

                            # 确保特征列表不为空
                            if not features:
                                st.warning("没有可用的特征进行相关性分析")
                            else:
                                # 确保所有特征都在数据框中
                                available_features = [f for f in features if f in processed_df.columns]
                                if '涨跌标签' in processed_df.columns:
                                    available_features.append('涨跌标签')

                                if len(available_features) < 2:
                                    st.warning("特征数量不足，无法进行相关性分析")
                                else:
                                    # 计算相关性矩阵
                                    corr_matrix = processed_df[available_features].corr()

                                    # 绘制热力图
                                    fig, ax = plt.subplots(figsize=(12, 10))
                                    mask = np.triu(np.ones_like(corr_matrix, dtype=bool))
                                    sns.heatmap(corr_matrix, mask=mask, annot=False, cmap='coolwarm',
                                                fmt='.2f', square=True, ax=ax)
                                    plt.title('特征相关性热力图')
                                    st.pyplot(fig)

                                    # 与目标变量的相关性
                                    if '涨跌标签' in corr_matrix.columns:
                                        target_corr = corr_matrix['涨跌标签'].drop('涨跌标签', errors='ignore').sort_values(ascending=False)

                                        if not target_corr.empty:
                                            # 显示与目标变量相关性最高的特征
                                            st.subheader("与涨跌标签相关性最高的特征")
                                            fig, ax = plt.subplots(figsize=(10, 6))
                                            sns.barplot(x=target_corr.values, y=target_corr.index, ax=ax)
                                            plt.title('特征与涨跌标签的相关性')
                                            st.pyplot(fig)
                        except Exception as e:
                            st.error(f"绘制相关性分析时出错: {str(e)}")
                            st.code(traceback.format_exc())

                    with tab4:
                        try:
                            # 预测结果分析
                            st.subheader("预测结果分析")

                            # 检查测试集是否为空
                            if len(X_test) == 0:
                                st.warning("测试集为空，无法进行预测结果分析")
                            else:
                                # 计算测试集上的预测概率
                                y_pred_proba_all = model.predict_proba(X_test)[:, 1]

                                # 创建预测结果数据框
                                pred_df = pd.DataFrame({
                                    '日期': X_test.index,
                                    '实际涨跌': y_test,
                                    '预测概率': y_pred_proba_all,
                                    '预测涨跌': y_pred
                                })

                                # 添加预测正确/错误标记
                                pred_df['预测结果'] = (pred_df['实际涨跌'] == pred_df['预测涨跌']).map({True: '正确', False: '错误'})

                                # 绘制预测概率分布
                                fig = px.histogram(pred_df, x='预测概率', color='实际涨跌',
                                                barmode='overlay', nbins=min(20, len(pred_df)),
                                                labels={'预测概率': '预测上涨概率', '实际涨跌': '实际结果'},
                                                color_discrete_map={0: 'red', 1: 'green'})
                                fig.update_layout(title='预测概率分布')
                                st.plotly_chart(fig, use_container_width=True)

                                # 绘制预测结果随时间变化
                                fig = px.line(pred_df, x='日期', y=['实际涨跌', '预测概率'])
                                fig.update_layout(title='预测结果随时间变化')
                                st.plotly_chart(fig, use_container_width=True)

                                # 显示最近的预测结果
                                st.subheader("最近的预测结果")
                                display_rows = min(10, len(pred_df))
                                st.dataframe(pred_df.tail(display_rows).style.format({'预测概率': '{:.2%}'}), use_container_width=True)
                        except Exception as e:
                            st.error(f"绘制预测结果分析时出错: {str(e)}")
                            st.code(traceback.format_exc())

                except Exception as e:
                    st.error(f"绘制可视化图表时出错: {str(e)}")
                    st.code(traceback.format_exc())

    except Exception as e:
        st.error(f"系统错误：{str(e)}")


# 主程序
if __name__ == "__main__":
    app()