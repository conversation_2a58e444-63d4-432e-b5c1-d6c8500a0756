import akshare as ak
import pandas as pd
from datetime import datetime, timedelta
import os
from tabulate import tabulate
import time
import sys
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.dates import DateFormatter, MinuteLocator
import numpy as np

# 确保 UTF-8 编码
sys.stdout.reconfigure(encoding='utf-8')

class AStockQuery:
    def __init__(self):
        current_dir = os.path.dirname(os.path.abspath(__file__))
        self.history_file = os.path.join(current_dir, "stock_query_history.txt")
        self.current_stock_code = None  # 当前查询的股票代码
        self.refresh_timer = None  # 刷新定时器
        print(f"当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    def save_to_history(self, stock_code, stock_name):
        """保存查询历史到本地文件，并去重"""
        try:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            new_record = f"{timestamp} - 股票代码: {stock_code}, 股票名称: {stock_name}\n"

            if not os.path.exists(self.history_file):
                # 如果文件不存在，直接创建并写入
                with open(self.history_file, 'w', encoding='utf-8') as f:
                    f.write(new_record)
                return

            # 读取现有历史记录
            with open(self.history_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # 过滤掉相同股票代码的旧记录
            filtered_lines = []
            existing_codes = set()

            # 先添加新记录
            filtered_lines.append(new_record)
            existing_codes.add(stock_code)

            # 再添加不重复的旧记录
            for line in lines:
                # 提取股票代码
                code_match = line.split("股票代码: ")[1].split(",")[0].strip() if "股票代码: " in line else None
                if code_match and code_match not in existing_codes:
                    filtered_lines.append(line)
                    existing_codes.add(code_match)

            # 按时间戳排序
            filtered_lines.sort(key=lambda x: datetime.strptime(x.split(" - ")[0], '%Y-%m-%d %H:%M:%S'))

            # 写入过滤后的记录
            with open(self.history_file, 'w', encoding='utf-8') as f:
                f.writelines(filtered_lines)

        except Exception as e:
            print(f"保存历史记录出错: {str(e)}")

    def show_history(self):
        """显示查询历史"""
        try:
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    history = f.read()
                if history:
                    print("\n=== 查询历史记录 ===")
                    print(history)
                else:
                    print("\n历史记录为空")
            else:
                print("\n暂无历史记录")
        except Exception as e:
            print(f"读取历史记录出错: {str(e)}")

    def _fetch_realtime_price(self, stock_code):
        """内部方法：从 AKShare 获取实时价格"""
        try:
            # 强制获取最新数据，不使用缓存
            import importlib
            importlib.reload(ak)
            df = ak.stock_zh_a_spot_em()
            stock_data = df[df['代码'] == stock_code]
            if not stock_data.empty:
                # 获取均线数据，需要更多天数来计算30日均线
                end_date = datetime.now().strftime('%Y%m%d')
                start_date = (datetime.now() - pd.Timedelta(days=60)).strftime('%Y%m%d')  # 增加获取的天数
                hist_data = ak.stock_zh_a_hist(symbol=stock_code, period="daily",
                                              start_date=start_date, end_date=end_date, adjust="qfq")

                # 计算各均线，使用rolling方法而不是tail
                if not hist_data.empty:
                    # 确保数据按日期排序
                    hist_data = hist_data.sort_values('日期')

                    # 计算各均线
                    ma10 = hist_data['收盘'].rolling(window=10).mean().iloc[-1] if len(hist_data) >= 10 else None
                    ma20 = hist_data['收盘'].rolling(window=20).mean().iloc[-1] if len(hist_data) >= 20 else None
                    ma30 = hist_data['收盘'].rolling(window=30).mean().iloc[-1] if len(hist_data) >= 30 else None
                else:
                    ma10 = ma20 = ma30 = None

                # 获取资金流向数据 - 尝试多种方法
                net_flow = total_inflow = total_outflow = None

                # 方法1: 尝试获取北向资金数据
                try:
                    # 获取当日个股北向资金数据
                    north_data = ak.stock_em_hsgt_north_net_flow_in(symbol=stock_code)
                    if not north_data.empty:
                        # 获取最新一天的数据
                        latest_north = north_data.iloc[-1]
                        net_flow = latest_north['净流入'] / 100000000  # 转换为亿元
                        total_inflow = latest_north['流入'] / 100000000  # 转换为亿元
                        total_outflow = latest_north['流出'] / 100000000  # 转换为亿元
                except:
                    pass

                # 方法2: 如果方法1失败，尝试获取个股资金流向数据
                if net_flow is None:
                    try:
                        # 获取个股资金流向数据
                        flow_data = ak.stock_individual_fund_flow(stock=stock_code)
                        if not flow_data.empty:
                            # 获取最新一天的数据
                            latest_flow = flow_data.iloc[0]
                            if '净流入' in flow_data.columns:
                                net_flow = latest_flow['净流入'] / 100000000  # 转换为亿元
                            if '流入资金' in flow_data.columns and '流出资金' in flow_data.columns:
                                total_inflow = latest_flow['流入资金'] / 100000000  # 转换为亿元
                                total_outflow = latest_flow['流出资金'] / 100000000  # 转换为亿元
                    except:
                        pass

                # 方法3: 如果前两种方法都失败，尝试计算资金流向
                if net_flow is None and not hist_data.empty and len(hist_data) > 1:
                    try:
                        # 使用最近两天的成交额和涨跌幅来估算资金流向
                        latest_day = hist_data.iloc[-1]
                        prev_day = hist_data.iloc[-2]

                        # 计算资金净流入（成交额 * 涨跌幅的比例）
                        change_percent = (latest_day['收盘'] - prev_day['收盘']) / prev_day['收盘'] * 100
                        amount = latest_day['成交额'] / 100000000  # 转换为亿元

                        # 简单估算：正涨幅表示净流入，负涨幅表示净流出
                        net_flow = amount * change_percent / 100

                        # 估算流入和流出
                        if net_flow > 0:
                            # 正净流入：总成交的一半加上净流入为流入，总成交减去流入为流出
                            total_inflow = amount/2 + net_flow
                            total_outflow = amount - total_inflow
                        else:
                            # 负净流入：总成交的一半减去净流入绝对值为流入，总成交减去流入为流出
                            total_inflow = amount/2 - abs(net_flow)
                            total_outflow = amount - total_inflow
                    except:
                        pass

                return {
                    'name': stock_data['名称'].values[0],
                    'price': stock_data['最新价'].values[0],
                    'change_percent': stock_data['涨跌幅'].values[0],
                    'amount': stock_data['成交额'].values[0] / 100000000,  # 转换为亿元
                    'ma10': ma10,  # 10日均线
                    'ma20': ma20,  # 20日均线
                    'ma30': ma30,   # 30日均线
                    'net_flow': net_flow,  # 资金净流入
                    'total_inflow': total_inflow,  # 资金总流入
                    'total_outflow': total_outflow   # 资金总流出
                }
            return None
        except Exception as e:
            print(f"获取实时价格出错: {str(e)}")
            return None

    def get_realtime_price(self, stock_code, auto_refresh=True):
        """获取股票实时价格"""
        try:
            # 保存当前查询的股票代码，用于自动刷新
            self.current_stock_code = stock_code

            while True:
                # 强制清除缓存，确保获取最新数据
                import importlib
                importlib.reload(ak)

                # 获取实时数据
                data = self._fetch_realtime_price(stock_code)

                if data:
                    stock_name = data['name']
                    current_price = data['price']
                    change_percent = data['change_percent']
                    amount = data['amount']
                    ma10 = data['ma10']
                    ma20 = data['ma20']
                    ma30 = data['ma30']
                    total_inflow = data['total_inflow']
                    total_outflow = data['total_outflow']

                    # 清屏，使显示更整洁
                    os.system('cls' if os.name == 'nt' else 'clear')
                    print("=== 🔄 实时行情 (每60秒自动刷新) ===\n")

                    print(f"\n股票代码: {stock_code}")
                    print(f"股票名称: {stock_name}")
                    print(f"当前价格: {current_price} 元")
                    print(f"涨跌幅: {change_percent}%")
                    print(f"成交额: {amount:.2f}亿")

                    # 显示资金流向数据
                    print("\n=== 资金流向 ===")
                    net_flow = data.get('net_flow')
                    total_inflow = data.get('total_inflow')
                    total_outflow = data.get('total_outflow')

                    if net_flow is not None:
                        print(f"资金净流入: {net_flow:+.2f}亿")

                        if total_inflow is not None and total_outflow is not None:
                            print(f"资金流入: {total_inflow:.2f}亿")
                            print(f"资金流出: {total_outflow:.2f}亿")

                            # 计算资金流向比例
                            if total_inflow + total_outflow > 0:
                                inflow_ratio = total_inflow / (total_inflow + total_outflow) * 100
                                print(f"流入比例: {inflow_ratio:.1f}%")
                    else:
                        print("资金流向数据不可用")

                    # 显示均线数据
                    print("\n=== 均线数据 ===")
                    if ma10 is not None:
                        price_vs_ma10 = ((current_price - ma10) / ma10) * 100
                        print(f"10日均线: {ma10:.2f} 元 ({price_vs_ma10:+.2f}%)")
                    else:
                        print("10日均线: 数据不足")

                    if ma20 is not None:
                        price_vs_ma20 = ((current_price - ma20) / ma20) * 100
                        print(f"20日均线: {ma20:.2f} 元 ({price_vs_ma20:+.2f}%)")
                    else:
                        print("20日均线: 数据不足")

                    if ma30 is not None:
                        price_vs_ma30 = ((current_price - ma30) / ma30) * 100
                        print(f"30日均线: {ma30:.2f} 元 ({price_vs_ma30:+.2f}%)")
                    else:
                        print("30日均线: 数据不足")

                    print(f"\n时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                    print("\n按 Ctrl+C 停止自动刷新")

                    self.save_to_history(stock_code, stock_name)

                    # 自动刷新
                    time.sleep(60)  # 等待60秒
                else:
                    print(f"未找到股票代码 {stock_code} 的实时数据")
                    break

        except KeyboardInterrupt:
            print("\n已停止自动刷新")
            return
        except Exception as e:
            print(f"查询实时价格出错: {str(e)}")

    def get_basic_info(self, stock_code):
        """获取股票基本信息"""
        try:
            df = ak.stock_individual_info_em(stock_code)
            stock_name = None
            for _, row in df.iterrows():
                if row['item'] == '股票名称':
                    stock_name = row['value']
                    break

            print(f"\n股票基本信息 ({stock_code}):")
            for _, row in df.iterrows():
                value = row['value']
                item = row['item']

                if item == '总股本':
                    print(f"{item}: {value} 股")
                elif item == '流通股':
                    print(f"{item}: {value} 股")
                elif item == '总市值':
                    print(f"{item}: {value} 亿元")
                elif item == '流通市值':
                    print(f"{item}: {value} 亿元")
                else:
                    print(f"{item}: {value}")

            if stock_name:
                self.save_to_history(stock_code, stock_name)
        except Exception as e:
            print(f"查询基本信息出错: {str(e)}")

    def _fetch_history_data(self, stock_code, start_date, end_date, period="daily", adjust="qfq"):
        """内部方法：从 AKShare 获取历史数据（实时获取）"""
        try:
            # 强制清除缓存，确保获取最新数据
            import importlib
            importlib.reload(ak)

            return ak.stock_zh_a_hist(
                symbol=stock_code,
                period=period,
                start_date=start_date,
                end_date=end_date,
                adjust=adjust
            )
        except Exception as e:
            print(f"获取历史数据出错: {str(e)}")
            return pd.DataFrame()

    def get_history_data(self, stock_code, period="daily", days=10):
        """获取股票历史数据"""
        try:
            # 注意：akshare不需要前缀，直接使用股票代码
            # 下面的代码在当前版本中不需要，但保留以便将来兼容其他API
            # if stock_code.startswith('6'):
            #     market_code = f"sh{stock_code}"
            # else:
            #     market_code = f"sz{stock_code}"

            end_date = datetime.now().strftime('%Y%m%d')
            start_date = (datetime.now() - pd.Timedelta(days=days+5)).strftime('%Y%m%d')

            # 直接获取最新数据，不使用缓存
            df = self._fetch_history_data(stock_code, start_date, end_date, period, "qfq")

            if df.empty:
                print(f"未获取到股票 {stock_code} 的历史数据，可能是代码错误或数据不可用")
                return

            stock_name_df = ak.stock_zh_a_spot_em()
            stock_name = stock_name_df[stock_name_df['代码'] == stock_code]['名称'].values[0] if not stock_name_df[stock_name_df['代码'] == stock_code].empty else "未知"

            # 计算涨跌幅
            if '收盘' in df.columns:
                df['涨跌幅'] = df['收盘'].pct_change() * 100
                df.loc[0, '涨跌幅'] = 0.0  # 第一天的涨跌幅设为0

            available_columns = [col for col in ['日期', '开盘', '收盘', '最高', '最低', '涨跌幅', '成交量', '成交额'] if col in df.columns]
            if not available_columns:
                print("返回的数据中没有预期的列，无法显示历史数据")
                return

            df_display = df[available_columns].tail(days)

            # 自定义表格显示函数
            def format_table(df, columns):
                # 处理数据
                rows = []
                for _, row in df.iterrows():
                    formatted_row = []
                    for col in columns:
                        value = row[col]
                        if col == '日期':
                            formatted_row.append(str(value))
                        elif col == '成交量' and pd.notna(value):
                            formatted_row.append(f"{value/10000:.2f}万")
                        elif col == '成交额' and pd.notna(value):
                            formatted_row.append(f"{value/100000000:.2f}亿")
                        elif col == '涨跌幅' and pd.notna(value):
                            formatted_row.append(f"{value:.2f}%")
                        elif pd.notna(value):
                            formatted_row.append(f"{value:.2f}")
                        else:
                            formatted_row.append("N/A")
                    rows.append(formatted_row)

                # 计算每列的最大宽度
                col_widths = [max(len(str(col)), max([len(str(row[i])) for row in rows])) for i, col in enumerate(columns)]

                # 生成表头分隔符
                separator = "+" + "-" * (col_widths[0] + 2) + "+"
                for i in range(1, len(columns)):
                    separator += "-" * (col_widths[i] + 2) + "+"

                # 生成表头行
                header_row = "|"
                for i, col in enumerate(columns):
                    header_row += f" {col.center(col_widths[i])} |"

                # 生成表格内容
                table_rows = []
                for row in rows:
                    table_row = "|"
                    for i, cell in enumerate(row):
                        table_row += f" {cell.center(col_widths[i])} |"
                    table_rows.append(table_row)

                # 组合表格
                table = separator + "\n" + header_row + "\n" + separator + "\n"
                table += ("\n" + separator + "\n").join(table_rows)
                table += "\n" + separator

                return table

            print(f"\n股票 {stock_code} ({stock_name}) 近{days}天历史数据:")

            # 使用自定义表格函数
            table = format_table(df_display, available_columns)
            print(table)

            self.save_to_history(stock_code, stock_name)

        except Exception as e:
            print(f"查询历史数据出错: {str(e)}")

    def show_minute_chart(self, stock_code):
        """显示股票分时图"""
        try:
            # 强制清除缓存，确保获取最新数据
            import importlib
            importlib.reload(ak)

            # 获取分时数据
            df = ak.stock_zh_a_hist_min_em(symbol=stock_code, period='1', adjust='')

            # 只保留当天的数据
            today = datetime.now().strftime('%Y-%m-%d')
            df['时间'] = pd.to_datetime(df['时间'])
            df = df[df['时间'].dt.strftime('%Y-%m-%d') == today]

            if df.empty:
                print("今日暂无交易数据")
                return

            # 只保留交易时间段的数据 (9:30-11:30, 13:00-15:00)
            df['hour'] = df['时间'].dt.hour
            df['minute'] = df['时间'].dt.minute
            trading_time_mask = (
                # 上午交易时段
                ((df['hour'] == 9) & (df['minute'] >= 30)) |
                (df['hour'] == 10) |
                ((df['hour'] == 11) & (df['minute'] <= 30)) |
                # 下午交易时段
                ((df['hour'] >= 13) & (df['hour'] < 15))
            )
            df = df[trading_time_mask]

            # 计算涨幅比
            df['涨幅比'] = df['收盘'].pct_change() * 100

            # 设置中文显示
            plt.rcParams['font.sans-serif'] = ['SimHei']
            plt.rcParams['axes.unicode_minus'] = False
            plt.style.use('dark_background')  # 使用深色主题

            # 创建图表
            _ = plt.figure(figsize=(15, 10))  # 不需要存储fig变量
            gs = plt.GridSpec(2, 1, height_ratios=[3, 1])
            ax1 = plt.subplot(gs[0])
            ax2 = plt.subplot(gs[1])

            # 绘制价格
            ax1.plot(df['时间'], df['收盘'], 'white', label='价格', linewidth=1.5)

            # 添加价格标记
            latest_price = df['收盘'].iloc[-1]
            ax1.axhline(y=latest_price, color='gray', linestyle='--', alpha=0.3)
            ax1.text(df['时间'].iloc[-1], latest_price, f' {latest_price:.2f}',
                     verticalalignment='bottom', color='white')

            # 设置网格和样式
            ax1.grid(True, linestyle='--', alpha=0.2)
            ax1.set_ylabel('价格(元)', color='white')

            # 绘制成交量
            volume_colors = ['#FF5252' if price >= df['收盘'].iloc[0] else '#69F0AE'
                            for price in df['收盘']]
            ax2.bar(df['时间'], df['成交量']/10000, color=volume_colors,
                    alpha=0.7, width=0.002)
            ax2.set_ylabel('成交量(万手)')
            ax2.grid(True, linestyle='--', alpha=0.2)

            # 设置x轴时间格式
            for ax in [ax1, ax2]:
                ax.xaxis.set_major_locator(MinuteLocator(byminute=[0, 30]))
                ax.xaxis.set_major_formatter(DateFormatter('%H:%M'))

            # 获取股票信息
            stock_info = ak.stock_zh_a_spot_em()
            stock_data = stock_info[stock_info['代码'] == stock_code].iloc[0]
            stock_name = stock_data['名称']
            current_price = stock_data['最新价']
            high_price = stock_data['最高']
            low_price = stock_data['最低']

            # 设置标题
            title = (f'{stock_code} {stock_name}  分时图  {datetime.now().strftime("%Y-%m-%d")}\n'
                    f'最新: {current_price:.2f}  最高: {high_price:.2f}  最低: {low_price:.2f}')
            plt.suptitle(title, fontsize=12, color='white')

            # 添加图例
            ax1.legend(loc='upper right', framealpha=0.3)

            # 自动调整布局
            plt.tight_layout()

            # 显示图表
            plt.show()

        except Exception as e:
            print(f"获取分时图数据出错: {str(e)}")

    def get_stock_code_by_name(self, stock_name):
        """根据股票名称查找股票代码"""
        try:
            # 直接获取最新股票列表，不使用缓存
            print("正在获取股票列表...")
            stock_list = ak.stock_zh_a_spot_em()

            # 在股票列表中查找匹配的股票名称
            matched_stocks = stock_list[stock_list['名称'].str.contains(stock_name)]

            if matched_stocks.empty:
                print(f"未找到名称包含 '{stock_name}' 的股票")
                return None
            elif len(matched_stocks) > 1:
                print(f"\n找到多个匹配的股票:")
                for i, (_, row) in enumerate(matched_stocks.iterrows(), 1):
                    print(f"{i}. 代码: {row['代码']}, 名称: {row['名称']}")

                choice = input("\n请选择一个股票 (输入序号): ")
                try:
                    choice_idx = int(choice) - 1
                    if 0 <= choice_idx < len(matched_stocks):
                        return matched_stocks.iloc[choice_idx]['代码']
                    else:
                        print("无效的选择")
                        return None
                except ValueError:
                    print("请输入有效的数字")
                    return None
            else:
                # 只找到一个匹配的股票
                stock_code = matched_stocks.iloc[0]['代码']
                stock_name = matched_stocks.iloc[0]['名称']
                print(f"找到股票: {stock_name} (代码: {stock_code})")
                return stock_code

        except Exception as e:
            print(f"查找股票代码出错: {str(e)}")
            return None

    def run(self):
        """主程序循环"""
        while True:
            print("\n=== 🐟鲤鱼股票系统 ===")
            print("1. 实时价格")
            print("2. 基本信息")
            print("3. 历史数据")
            print("4. 查询历史")
            print("5. 分时图")
            print("Q. 退出系统")
            print("========================")
            choice = input("请选择功能或直接输入股票代码: ")

            if choice.lower() == 'q':
                print("欢迎常来")
                break

            if choice in ['1', '2', '3', '4', '5']:
                if choice == '4':
                    self.show_history()
                else:
                    input_text = input("请输入股票代码或名称 (如 600519 或 贵州茅台): ")

                    # 判断输入是代码还是名称
                    if input_text.isdigit() or (len(input_text) == 6 and input_text.isalnum()):
                        # 输入是股票代码
                        stock_code = input_text
                    else:
                        # 输入是股票名称，尝试查找对应的代码
                        stock_code = self.get_stock_code_by_name(input_text)
                        if not stock_code:
                            continue

                    if choice == '1':
                        self.get_realtime_price(stock_code)
                    elif choice == '2':
                        self.get_basic_info(stock_code)
                    elif choice == '3':
                        days = input("请输入查询天数 (默认10天): ") or 10
                        self.get_history_data(stock_code, days=int(days))
                    elif choice == '5':
                        self.show_minute_chart(stock_code)
            else:
                # 非菜单选项，视为直接输入股票代码或名称，执行实时价格查询
                input_text = choice

                # 判断输入是代码还是名称
                if input_text.isdigit() or (len(input_text) == 6 and input_text.isalnum()):
                    # 输入是股票代码
                    stock_code = input_text
                else:
                    # 输入是股票名称，尝试查找对应的代码
                    stock_code = self.get_stock_code_by_name(input_text)
                    if not stock_code:
                        continue

                # 执行实时价格查询
                self.get_realtime_price(stock_code)

if __name__ == "__main__":
    stock_query = AStockQuery()
    stock_query.run()