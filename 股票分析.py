
import streamlit as st
import sys
import importlib.util

# 检查必要的依赖包
required_packages = {
    "akshare": "akshare",
    "plotly.graph_objects": "plotly",
    "pandas": "pandas",
    "requests": "requests",
    "os": None,          # 标准库
    "datetime": None     # 标准库
}

missing_packages = []
for module, package in required_packages.items():
    if package and not importlib.util.find_spec(module.split('.')[0]):
        missing_packages.append(package)

if missing_packages:
    st.error(f"缺少必要的依赖包: {', '.join(missing_packages)}")
    st.info("请运行以下命令安装缺失的依赖包:")
    st.code(f"pip install {' '.join(missing_packages)}")
    st.stop()

# 导入必要的库
import akshare as ak
import plotly.graph_objects as go
from datetime import datetime, timedelta
import pandas as pd
import requests
import os

# API配置
API_BASE = "https://api.siliconflow.cn"
API_KEY = "sk-rhcurhycrnawfucgktvqhmxqiedwtiqpmocvokusbffdmdzv"
MODEL_NAME = "Qwen/QwQ-32B"

# 获取热门股票（按成交量排序）
@st.cache_data(ttl=1800)  # 缓存30分钟
def get_hot_stocks(limit=5):
    try:
        # 获取A股实时行情
        df = ak.stock_zh_a_spot_em()
        # 按成交量排序，获取前N只热门股票
        hot_stocks = df.sort_values(by="成交量", ascending=False).head(limit)
        # 返回股票代码和名称
        return [(row["代码"], row["名称"]) for _, row in hot_stocks.iterrows()]
    except Exception as e:
        print(f"获取热门股票失败: {str(e)}")
        # 返回默认热门股票
        return [("600519", "贵州茅台"), ("601318", "中国平安"),
                ("000858", "五粮液"), ("600036", "招商银行"),
                ("601166", "兴业银行")]

# 读取历史搜索记录
def get_search_history(limit=5):
    try:
        with open("stock_query_history.txt", "r", encoding="utf-8") as f:
            lines = f.readlines()

        # 解析历史记录
        history = []
        for line in reversed(lines):  # 从最新的记录开始
            if not line.strip():
                continue
            parts = line.split(" - 股票代码: ")
            if len(parts) != 2:
                continue
            code_name = parts[1].strip().split(", 股票名称: ")
            if len(code_name) != 2:
                continue
            code, name = code_name
            # 避免重复
            if (code, name) not in history:
                history.append((code, name))
            if len(history) >= limit:
                break
        return history
    except Exception as e:
        print(f"读取历史记录失败: {str(e)}")
        return []

# 更新搜索历史
def update_search_history(stock_code, stock_name):
    try:
        # 获取当前时间
        now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        # 写入历史记录，确保股票名称正确写入
        with open("stock_query_history.txt", "a", encoding="utf-8") as f:
            f.write(f"{now} - 股票代码: {stock_code}, 股票名称: {stock_name}\n")
        print(f"已更新搜索历史: 股票代码={stock_code}, 股票名称={stock_name}")
        return True
    except Exception as e:
        print(f"更新历史记录失败: {str(e)}")
        return False

# 获取股票数据（带缓存）
@st.cache_data(ttl=3600)
def get_stock_data(stock_code):
    end_date = datetime.now().strftime('%Y%m%d')
    start_date = (datetime.now() - timedelta(days=90)).strftime('%Y%m%d')
    try:
        df = ak.stock_zh_a_hist(symbol=stock_code, period="daily",
                                start_date=start_date, end_date=end_date, adjust="qfq")
        return df[['日期', '开盘', '最高', '最低', '收盘', '成交量']]
    except Exception as e:
        st.error(f"数据获取失败: {str(e)}")
        return None

# 技术指标计算
def calculate_technical_indicators(df):
    # 计算移动平均线
    df['MA5'] = df['收盘'].rolling(5).mean()
    df['MA20'] = df['收盘'].rolling(20).mean()
    # 计算RSI
    delta = df['收盘'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
    df['RSI'] = 100 - (100 / (1 + gain / loss))
    return df.dropna()

# AI分析模块
def analyze_with_ai(stock_code, df, news):
    # 构建提示词
    prompt = f"""作为资深证券分析师，请基于以下数据给出专业分析：
    股票代码：{stock_code}
    最新收盘价：{df['收盘'].iloc[-1]}
    关键指标：
    - 5日均线：{df['MA5'].iloc[-1]}
    - 20日均线：{df['MA20'].iloc[-1]}
    - RSI指数：{df['RSI'].iloc[-1]}
    近期新闻摘要：
    {"\n".join(news['新闻标题'].head(3))}
    请从以下维度给出分析：
    1. 趋势判断（技术面）
    2. 量价关系分析
    3. 新闻事件影响评估
    4. 短期操作建议
    5. 风险提示"""

    # 直接使用requests调用API
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {API_KEY}"
    }

    payload = {
        "model": MODEL_NAME,
        "messages": [{"role": "user", "content": prompt}],
        "temperature": 0.3,
        "max_tokens": 2048
    }

    try:
        # 确保URL格式正确
        api_url = f"{API_BASE}/v1/chat/completions"
        print(f"调用API: {api_url}")

        response = requests.post(
            api_url,
            headers=headers,
            json=payload,
            timeout=600  # 增加超时时间
        )
        response.raise_for_status()  # 检查请求是否成功

        # 获取响应文本并尝试清理无效字符
        response_text = response.text

        # 打印原始响应以便调试
        print("API原始响应:")
        print(response_text[:200] + "..." if len(response_text) > 200 else response_text)

        # 直接尝试提取内容，不解析JSON
        if "content" in response_text:
            try:
                # 尝试解析JSON
                import json
                import re

                # 清理可能的无效控制字符
                cleaned_text = re.sub(r'[\x00-\x1F\x7F]', '', response_text)
                result = json.loads(cleaned_text)
                analysis = result.get("choices", [{}])[0].get("message", {}).get("content", "分析生成失败")
            except Exception as json_error:
                print(f"JSON解析错误: {str(json_error)}")
                # 如果JSON解析失败，尝试直接提取内容
                try:
                    # 使用正则表达式尝试提取content部分
                    content_match = re.search(r'"content"\s*:\s*"([^"]*)"', cleaned_text)
                    if content_match:
                        analysis = content_match.group(1)
                    else:
                        analysis = "无法解析API响应"
                except Exception as regex_error:
                    print(f"正则提取错误: {str(regex_error)}")
                    analysis = "无法解析API响应"
        else:
            # 如果响应中没有content关键字，可能是API直接返回文本内容
            # 检查响应是否看起来像是分析文本（而不是错误消息）
            if len(response_text) > 100 and not response_text.startswith('{"'):
                # 可能是直接返回的文本内容
                analysis = response_text
            else:
                # 可能是API格式不同或错误
                analysis = "API返回格式不符合预期"
        print(analysis)
        return analysis
    except Exception as e:
        print(f"API调用错误: {str(e)}")
        return f"分析生成失败: {str(e)}"
# 此处移除了show_analysis_in_browser函数，因为我们不再需要在浏览器中展示分析结果

# 主程序
def main():
    try:
        st.set_page_config(page_title="智能股票分析系统", layout="wide")
    except Exception as e:
        # 如果set_page_config已经被调用，会抛出异常，但这不影响程序运行
        pass

    st.title("智能股票分析系统")

    # 创建搜索框和分析按钮
    st.markdown("### 请输入股票代码进行分析")

    # 创建一个更美观的搜索框布局
    col1, col2 = st.columns([4, 1])
    with col1:
        stock_code = st.text_input('股票代码', '', placeholder='例如：600519（贵州茅台）')
    with col2:
        analyze_button = st.button('开始分析', type="primary")

    # 创建两列布局用于历史搜索和热门股票
    hist_col, hot_col = st.columns(2)

    # 左侧列：历史搜索
    with hist_col:
        st.caption("历史搜索:")
        # 获取历史搜索记录
        search_history = get_search_history(5)
        # 创建选项列表，格式为 "股票代码 (股票名称)"
        history_options = [""] + [f"{code} ({name})" for code, name in search_history]
        history_select = st.selectbox(
            "历史搜索记录",
            options=history_options,
            label_visibility="collapsed"
        )
        # 如果选择了历史记录且股票代码输入框为空，则只填入股票代码
        if history_select and not stock_code:
            # 提取股票代码部分（第一个空格前的内容）
            stock_code = history_select.split()[0]

    # 右侧列：热门股票
    with hot_col:
        st.caption("热门股票:")
        # 获取热门股票
        hot_stocks = get_hot_stocks(5)
        # 创建选项列表，格式为 "股票代码 (股票名称)"
        hot_options = [""] + [f"{code} ({name})" for code, name in hot_stocks]
        hot_select = st.selectbox(
            "热门股票",
            options=hot_options,
            label_visibility="collapsed"
        )
        # 如果选择了热门股票且股票代码输入框为空，则只填入股票代码
        if hot_select and not stock_code:
            # 提取股票代码部分（第一个空格前的内容）
            stock_code = hot_select.split()[0]

    # 只有当用户点击分析按钮且输入了股票代码时才进行分析
    if analyze_button and stock_code:
        # 获取股票名称
        try:
            stock_info = ak.stock_individual_info_em(symbol=stock_code)
            stock_name = stock_info.iloc[0, 0] if not stock_info.empty else "未知"
            # 确保股票名称不为空
            if stock_name and stock_name != "未知":
                # 更新搜索历史
                update_search_history(stock_code, stock_name)
            else:
                print(f"无法获取股票名称，股票代码: {stock_code}")
        except Exception as e:
            print(f"获取股票信息异常: {str(e)}")
            stock_name = "未知"

        # 数据获取模块
        df = get_stock_data(stock_code)
        if df is not None:
            df = calculate_technical_indicators(df)

            # 创建两列布局
            col_chart, col_news = st.columns([2, 1])

            # 左侧列：K线图
            with col_chart:
                # K线图绘制
                fig = go.Figure(data=[
                    go.Candlestick(x=df['日期'], open=df['开盘'],
                                   high=df['最高'], low=df['最低'], close=df['收盘'],
                                   increasing_line_color='red', decreasing_line_color='green'),
                    go.Scatter(x=df['日期'], y=df['MA5'], name='5日均线'),
                    go.Scatter(x=df['日期'], y=df['MA20'], name='20日均线')
                ])
                fig.update_layout(title=f'{stock_code} 技术分析', height=500)
                st.plotly_chart(fig, use_container_width=True)

            # 右侧列：新闻
            with col_news:
                # 新闻获取模块
                try:
                    news = ak.stock_news_em(symbol=stock_code).head(5)
                    st.subheader("最新市场动态")
                    for _, row in news.iterrows():
                        st.markdown(f"**[{row['新闻标题']}]({row['新闻链接']})** - {row['发布时间']}")
                except Exception as e:
                    st.warning(f"新闻获取异常：{str(e)}")
                    news = pd.DataFrame({"新闻标题": ["无法获取新闻"]})

            # AI分析模块
            with st.spinner("AI分析中..."):
                analysis = analyze_with_ai(stock_code, df, news)

                # 在Streamlit界面显示分析结果
                # 创建标题
                st.subheader("量化策略建议")

                # 生成唯一ID，用于JavaScript选择元素
                element_id = f"analysis_{datetime.now().strftime('%Y%m%d%H%M%S')}"

                # 创建一个纯JavaScript的复制按钮，不使用Streamlit的按钮组件
                copy_button_html = f"""
                <div style="display:flex; justify-content:flex-end; margin-bottom:5px;">
                    <button
                        id="copy_button_{element_id}"
                        onclick="copyAnalysis('{element_id}')"
                        style="background-color:#4CAF50; color:white; border:none;
                               border-radius:4px; padding:5px 10px; cursor:pointer;
                               font-size:14px;">
                        📋 复制
                    </button>
                </div>

                <script>
                function copyAnalysis(id) {{
                    // 获取要复制的文本
                    const text = `{analysis.replace('`', '\\`').replace("'", "\\'")}`;

                    // 复制到剪贴板
                    navigator.clipboard.writeText(text)
                        .then(() => {{
                            // 显示成功消息
                            const button = document.getElementById('copy_button_' + id);
                            const originalText = button.innerHTML;
                            button.innerHTML = '✓ 已复制';
                            button.style.backgroundColor = '#45a049';

                            // 2秒后恢复按钮原样
                            setTimeout(() => {{
                                button.innerHTML = originalText;
                                button.style.backgroundColor = '#4CAF50';
                            }}, 2000);
                        }})
                        .catch(err => {{
                            console.error('复制失败:', err);
                            alert('复制失败，请手动复制');
                        }});
                }}
                </script>
                """

                # 显示复制按钮
                st.markdown(copy_button_html, unsafe_allow_html=True)

                # 使用更紧凑的样式显示分析结果
                st.markdown(f"""<div style="background-color: #000000; padding: 8px;
                            border-left: 4px solid #2196F3; white-space: pre-wrap;
                            font-family: 'Microsoft YaHei', Consolas, monospace;
                            font-size: 0.85em; line-height: 1.25; margin-top: -5px;
                            margin-bottom: 10px; border-radius: 0 4px 4px 0;">
                            {analysis}</div>""", unsafe_allow_html=True)

    # 如果没有输入股票代码，显示提示信息
    elif analyze_button and not stock_code:
        st.warning("请输入股票代码后再进行分析")

    # 初始状态下的提示信息和使用说明
    if not analyze_button and not stock_code:
        st.info("👆 请在上方输入股票代码，然后点击'开始分析'按钮")

        # 添加使用说明
        with st.expander("使用说明", expanded=False):
            st.markdown("""
            ### 如何使用本系统

            1. 在搜索框中输入股票代码（例如：600519）
            2. 点击"开始分析"按钮
            3. 系统将获取股票数据并进行分析
            4. 分析结果将在界面显示

            ### 功能特点

            - **技术分析**：展示K线图、移动平均线等技术指标
            - **新闻分析**：获取最新相关新闻
            - **AI分析**：通过AI模型分析股票走势和投资建议
            - **复制功能**：可一键复制量化策略建议

            ### 注意事项

            - 本系统仅提供数据分析参考，不构成投资建议
            - 投资有风险，入市需谨慎
            """)

    st.caption("免责声明：本工具仅提供数据分析参考，不构成投资建议")
if __name__ == "__main__":
    # 处理直接运行脚本的情况
    import sys
    if len(sys.argv) > 1 and sys.argv[0].endswith('.py'):
        print("请使用 'streamlit run 股票分析.py' 命令运行此脚本")
        print("正在尝试自动启动...")
        try:
            import subprocess
            import os
            script_path = os.path.abspath(__file__)
            subprocess.run(["streamlit", "run", script_path])
        except Exception as e:
            print(f"自动启动失败: {e}")
            print("请手动使用 'streamlit run 股票分析.py' 命令运行")
    else:
        main()