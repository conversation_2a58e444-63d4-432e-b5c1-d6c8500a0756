# 股票分析应用

这是一个基于Streamlit的股票分析应用程序，可以获取股票数据、进行技术分析并预测股票走势。

## 本地运行

```bash
python -m streamlit run test.py
```

## Docker部署

### 方法一：使用Docker Compose（推荐）

1. 确保已安装Docker和Docker Compose
2. 在项目根目录下运行：

```bash
docker-compose up -d
```

3. 访问 http://localhost:8501 使用应用程序
4. 停止应用：

```bash
docker-compose down
```

### 方法二：直接使用Docker

1. 构建Docker镜像：

```bash
docker build -t stock-analysis-app .
```

2. 运行Docker容器：

```bash
docker run -d -p 8501:8501 --name stock-app stock-analysis-app
```

3. 访问 http://localhost:8501 使用应用程序
4. 停止并删除容器：

```bash
docker stop stock-app
docker rm stock-app
```
